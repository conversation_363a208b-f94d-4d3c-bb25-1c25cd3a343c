package com.youying.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Comment;
import com.youying.common.core.domain.entity.RepertoireInfoDetail;
import com.youying.common.core.domain.entity.UserInteraction;
import com.youying.common.core.domain.entity.UserReceivingRecords;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.LookFlag;
import com.youying.common.exception.ServiceException;
import com.youying.common.utils.DateUtils;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.comment.CommentRequest;
import com.youying.system.domain.comment.CommentResponse;
import com.youying.system.domain.comment.SaveCommentRequest;
import com.youying.system.service.CommentService;
import com.youying.system.service.RepertoireInfoDetailService;
import com.youying.system.service.UserInteractionService;
import com.youying.system.service.UserReceivingRecordsService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 剧目剧场评论
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/comment")
public class CommentController extends BaseController {
    @Autowired
    private CommentService commentService;
    @Autowired
    private UserReceivingRecordsService userReceivingRecordsService;
    @Autowired
    private RepertoireInfoDetailService repertoireInfoDetailService;
    @Autowired
    private UserInteractionService userInteractionService;

    /**
     * 查询剧目剧场评论列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<CommentResponse>> listByPage(@RequestBody CommentRequest request) {
        startPage(request);
        return R.ok(getTableList(commentService.listByPage(request)));
    }

    /**
     * 查询用户评论列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByUser")
    public R<TableList<CommentResponse>> listByUser(@RequestBody CommentRequest request) {
        startPage(request);
        return R.ok(getTableList(commentService.listByUser(request)));
    }

    /**
     * 查询用户评论条数
     *
     * @return
     */
    @PostMapping(value = "/listByUserCount")
    public R<?> listByUserCount(@RequestBody CommentRequest request) {
        return R.ok(commentService.listByUserCount(request));
    }

    /**
     * 查询用户回复列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listReplyByUser")
    public R<TableList<CommentResponse>> listReplyByUser(@RequestBody CommentRequest request) {
        List<UserInteraction> userInteractionList = userInteractionService.list(new LambdaQueryWrapper<UserInteraction>()
                .eq(UserInteraction::getReplyUserId, SecurityUtils.getUserId())
                .eq(UserInteraction::getType, 1)
                .eq(UserInteraction::getLookFlag, LookFlag.DEFAULT.getCode()));
        if (CollectionUtils.isNotEmpty(userInteractionList)) {
            userInteractionList.stream().forEach(item -> {
                item.setLookFlag(LookFlag.PASS.getCode());
            });
            userInteractionService.updateBatchById(userInteractionList);
        }
        startPage(request);
        return R.ok(getTableList(commentService.listReplyByUser(request)));
    }

    /**
     * 查询剧场、剧目对应评论（填加评论时的两段评论）
     *
     * @return
     */
    @PostMapping(value = "/findMappingComment")
    public R<CommentResponse> findMappingComment(Long id) {
        return R.ok(commentService.findMappingComment(id));
    }

    /**
     * 查询评论详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<CommentResponse> details(Long id, Long userId) {
        return R.ok(commentService.details(id, userId));
    }

    /**
     * 添加剧目剧场评论
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加剧目剧场评论数据", businessType = BusinessType.INSERT)
    public R<?> add(@Validated @RequestBody SaveCommentRequest request) {
        // 判断是否有资格添加
        Long userLook = userReceivingRecordsService.findUserLook(request.getRepertoireId(), request.getTheaterId(), request.getRepertoireInfoDetailId());
        if (userLook == 0) {
            return R.fail("抱歉，只有观看过戏剧的观众才可以参与评价，您可以在别人的评论下发表您的点评");
        }
        RepertoireInfoDetail repertoireInfoDetail = repertoireInfoDetailService.getById(request.getRepertoireInfoDetailId());
        if (DateUtils.compareTo(repertoireInfoDetail.getEndTime(), new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS) < 0) {
            throw new ServiceException("请在观演结束后再试试评论吧~");
        }
        if (request.getUserReceivingRecordsId() != null && request.getUserReceivingRecordsId() > 0) {
            // 判断是否添加过评论
            UserReceivingRecords records = userReceivingRecordsService.getById(request.getUserReceivingRecordsId());
            if (records == null || (records.getCommentId() != null && records.getCommentId() > 0L)) {
                return R.fail("已评价，无法继续评价");
            }
        }
        return R.ok(commentService.add(request));
    }

    /**
     * 回复评论
     *
     * @return
     */
    @PostMapping(value = "/reply")
    @Log(title = "回复评论", businessType = BusinessType.INSERT)
    public R<?> reply(@RequestBody Comment comment) {
        return R.ok(commentService.reply(comment));
    }

    /**
     * 修改剧目剧场评论
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改剧目剧场评论数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody Comment comment) {
        return R.ok(commentService.updateById(comment));
    }

    /**
     * 删除剧目剧场评论
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除剧目剧场评论数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(commentService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

    /**
     * 查询用户回复数量
     *
     * @return
     */
    @GetMapping(value = "/findReplyByUserCount")
    public R<?> findReplyByUserCount() {
        return R.ok(commentService.count(new LambdaQueryWrapper<Comment>()
                .eq(Comment::getUserId, SecurityUtils.getUserId())
                .eq(Comment::getLookFlag, LookFlag.DEFAULT.getCode())
                .gt(Comment::getReplyId, 0L)
                .or()
                .eq(Comment::getUserId, SecurityUtils.getUserId())
                .eq(Comment::getLookFlag, LookFlag.DEFAULT.getCode())
                .gt(Comment::getUserMerchantId, 0L)));
    }
}

