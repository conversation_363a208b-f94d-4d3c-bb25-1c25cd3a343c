package com.youying.system.domain.comment;

import com.youying.common.core.page.PageDomain;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-06-08
 */
@Data
public class CommentRequest extends PageDomain {
    /**
     * 剧目1，剧场2 默认为：1
     */
    private Integer flag = 1;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 用户ID 未登录传 0
     */
    private Long userId;

    /**
     * 回复人ID
     */
    private Long replyId;

    /**
     * 父级ID 默认查顶级评论
     */
    private Long parentId = 0L;

    /**
     * 评论标志 0为评论 1已评论
     */
    private Long commentFlag;
}
