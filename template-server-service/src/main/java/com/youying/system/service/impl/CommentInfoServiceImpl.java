package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Comment;
import com.youying.common.core.domain.entity.CommentInfo;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.enums.Enums.UserInteractionFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.mapper.CommentInfoMapper;
import com.youying.system.service.CommentInfoService;
import com.youying.system.service.CommentService;
import com.youying.system.service.UserInteractionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 评论点赞、踩详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class CommentInfoServiceImpl extends ServiceImpl<CommentInfoMapper, CommentInfo> implements CommentInfoService {
    @Autowired
    private UserInteractionService userInteractionService;
    @Autowired
    private CommentService commentService;

    /**
     * 添加评论点赞、踩、删除
     *
     * @param commentInfo
     * @return
     */
    @Override
    @Transactional
    public Long addOrUpdate(CommentInfo commentInfo) {
        if (commentInfo.getType() == null) {
            baseMapper.deleteUserComment(commentInfo.getCommentId(), SecurityUtils.getUserId());
            userInteractionService.delete(commentInfo.getCommentId(), SecurityUtils.getUserId(), UserInteractionFlag.COMMENT_KUDOS.getCode());
            return commentInfo.getCommentId();
        }

        CommentInfo info = getOne(new LambdaQueryWrapper<CommentInfo>()
                .eq(CommentInfo::getCommentId, commentInfo.getCommentId())
                .eq(CommentInfo::getUserId, SecurityUtils.getUserId()));

        if (info != null) {
            remove(new LambdaQueryWrapper<CommentInfo>()
                    .eq(CommentInfo::getCommentId, commentInfo.getCommentId())
                    .eq(CommentInfo::getUserId, SecurityUtils.getUserId()));
        }
        save(commentInfo);

        // 判断是否为点赞
        if (StatusFlag.OK.getCode().equals(commentInfo.getType())) {
            Comment comment = commentService.getById(commentInfo.getCommentId());
            Long userId = comment.getReplyId() == null || comment.getReplyId() == 0L ? comment.getUserId() : comment.getReplyId();
            if (!SecurityUtils.getUserId().equals(userId)) {
                // 判断是否为为自己点赞
                userInteractionService.add(commentInfo.getCommentId(), null, userId, comment.getRepertoireId(), UserInteractionFlag.COMMENT_KUDOS.getCode());
            }
        } else {
            userInteractionService.delete(commentInfo.getCommentId(), SecurityUtils.getUserId(), UserInteractionFlag.COMMENT_KUDOS.getCode());
        }

        return commentInfo.getCommentId();
    }
}
