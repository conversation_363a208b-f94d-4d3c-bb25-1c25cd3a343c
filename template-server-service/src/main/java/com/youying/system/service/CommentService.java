package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.Comment;
import com.youying.system.domain.comment.CommentRequest;
import com.youying.system.domain.comment.CommentResponse;
import com.youying.system.domain.comment.SaveCommentRequest;

import java.util.List;

/**
 * <p>
 * 剧目剧场评论 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface CommentService extends IService<Comment> {

    /**
     * 剧目剧场评论列表
     *
     * @param request
     * @return
     */
    List<CommentResponse> listByPage(CommentRequest request);

    /**
     * 查询剧场、剧目对应评论（填加评论时的两段评论）
     *
     * @param id
     * @return
     */
    CommentResponse findMappingComment(Long id);

    /**
     * 添加评论
     *
     * @param request
     * @return
     */
    Long add(SaveCommentRequest request);

    /**
     * 查询用户是否添加评论
     *
     * @param repertoireId
     * @param theaterId
     * @param repertoireInfoDetailId
     * @return
     */
    Long findUserAdd(Long repertoireId, Long theaterId, Long repertoireInfoDetailId);

    /**
     * 查询评论详情
     *
     * @param id
     * @param userId
     * @return
     */
    CommentResponse details(Long id, Long userId);

    /**
     * 回复评论
     *
     * @param comment
     * @return
     */
    Long reply(Comment comment);

    /**
     * 查询用户评论列表(分页)
     *
     * @param request
     * @return
     */
    List<CommentResponse> listByUser(CommentRequest request);

    /**
     * 查询用户回复列表(分页)
     *
     * @param request
     * @return
     */
    List<CommentResponse> listReplyByUser(CommentRequest request);

    /**
     * 查询用户评论条数
     *
     * @param request
     * @return
     */
    Long listByUserCount(CommentRequest request);
}
